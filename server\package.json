{"name": "alicartify-server", "version": "1.0.0", "description": "Alicartify - Advanced multi-vendor eCommerce platform server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "test:auth": "node test-auth.js", "test:db": "node test-db-connection.js", "setup:mongodb": "node setup-mongodb.js", "generate-secrets": "node generate-secrets.js", "migrate:prod": "node scripts/migrate-to-production.js migrate", "migrate:verify": "node scripts/migrate-to-production.js verify", "migrate:backup": "node scripts/migrate-to-production.js backup-only", "test:connections": "node scripts/test-db-connections.js", "init:categories": "node scripts/initializeCategories.js", "reset:categories": "node scripts/resetCategories.js", "webhook:dev": "node start-webhook-dev.js", "dev:simple": "node start-dev.js", "test:ngrok": "node test-ngrok.js", "kill-ngrok": "node kill-ngrok.js", "test:webhook": "node test-razorpay-webhook.js", "test:shiprocket": "node scripts/test-shiprocket-config.js"}, "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.2.0", "express": "^4.19.2", "express-async-handler": "^1.2.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.10", "mongoose": "^8.16.3", "morgan": "^1.10.0", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.9.8", "qrcode": "^1.5.3", "razorpay": "^2.9.6", "speakeasy": "^2.0.0", "twilio": "^5.8.0", "xss": "^1.0.14"}, "devDependencies": {"axios": "^1.6.2", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.4"}, "keywords": ["ecommerce", "multi-vendor", "marketplace", "nodejs", "express", "mongodb"], "author": "Your Name", "license": "MIT"}