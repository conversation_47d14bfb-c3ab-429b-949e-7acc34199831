const { Setting } = require('../../models');
const shiprocketService = require('../../services/shiprocketService');

/**
 * Get all system settings
 */
const getSettings = async (req, res) => {
  try {
    const settings = await Setting.find({}).lean();

    // Convert array to object for easier frontend consumption
    const settingsObject = {};
    settings.forEach(setting => {
      settingsObject[setting.key] = {
        value: setting.value,
        type: setting.type,
        description: setting.description,
        category: setting.category
      };
    });

    res.json({
      success: true,
      data: settingsObject
    });

  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get settings by category
 */
const getSettingsByCategory = async (req, res) => {
  try {
    const { category } = req.params;

    const settings = await Setting.find({ category }).lean();

    // Convert array to object
    const settingsObject = {};
    settings.forEach(setting => {
      settingsObject[setting.key] = {
        value: setting.value,
        type: setting.type,
        description: setting.description
      };
    });

    res.json({
      success: true,
      data: settingsObject
    });

  } catch (error) {
    console.error('Get settings by category error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update system settings
 */
const updateSettings = async (req, res) => {
  try {
    const { settings } = req.body;

    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Settings object is required'
      });
    }

    const updatePromises = [];

    // Update each setting
    for (const [key, value] of Object.entries(settings)) {
      updatePromises.push(
        Setting.findOneAndUpdate(
          { key },
          {
            key,
            value,
            updatedAt: new Date(),
            updatedBy: req.user.userId
          },
          {
            upsert: true,
            new: true,
            setDefaultsOnInsert: true
          }
        )
      );

      // Update process.env for environment variables
      const envKey = key.toUpperCase();
      if (process.env.hasOwnProperty(envKey) || key.includes('_')) {
        process.env[envKey] = value;
        console.log(`Updated environment variable: ${envKey}`);
      }
    }

    await Promise.all(updatePromises);

    res.json({
      success: true,
      message: 'Settings updated successfully'
    });

  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update single setting
 */
const updateSetting = async (req, res) => {
  try {
    const { key } = req.params;
    const { value, type, description, category } = req.body;

    const updateData = {
      key,
      value,
      updatedAt: new Date(),
      updatedBy: req.user.userId
    };

    if (type) updateData.type = type;
    if (description) updateData.description = description;
    if (category) updateData.category = category;

    const setting = await Setting.findOneAndUpdate(
      { key },
      updateData,
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true
      }
    );

    res.json({
      success: true,
      data: setting,
      message: 'Setting updated successfully'
    });

  } catch (error) {
    console.error('Update setting error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update setting',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete setting
 */
const deleteSetting = async (req, res) => {
  try {
    const { key } = req.params;

    const setting = await Setting.findOneAndDelete({ key });

    if (!setting) {
      return res.status(404).json({
        success: false,
        message: 'Setting not found'
      });
    }

    res.json({
      success: true,
      message: 'Setting deleted successfully'
    });

  } catch (error) {
    console.error('Delete setting error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete setting',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Initialize default settings
 */
const initializeDefaultSettings = async (req, res) => {
  try {
    const defaultSettings = [
      // General Settings
      { key: 'site_name', value: 'Multi-Vendor eCommerce', type: 'string', category: 'general', description: 'Site name' },
      { key: 'site_description', value: 'Your ultimate shopping destination', type: 'string', category: 'general', description: 'Site description' },
      { key: 'currency', value: 'USD', type: 'string', category: 'general', description: 'Default currency' },
      { key: 'timezone', value: 'UTC', type: 'string', category: 'general', description: 'Default timezone' },
      { key: 'maintenance_mode', value: false, type: 'boolean', category: 'general', description: 'Maintenance mode' },
      { key: 'user_registration', value: true, type: 'boolean', category: 'general', description: 'Allow user registration' },
      { key: 'vendor_registration', value: true, type: 'boolean', category: 'general', description: 'Allow vendor registration' },
      { key: 'guest_checkout', value: true, type: 'boolean', category: 'general', description: 'Allow guest checkout' },

      // Email Settings
      { key: 'smtp_host', value: '', type: 'string', category: 'email', description: 'SMTP host' },
      { key: 'smtp_port', value: 587, type: 'number', category: 'email', description: 'SMTP port' },
      { key: 'smtp_username', value: '', type: 'string', category: 'email', description: 'SMTP username' },
      { key: 'smtp_password', value: '', type: 'string', category: 'email', description: 'SMTP password' },
      { key: 'from_email', value: '', type: 'string', category: 'email', description: 'From email address' },
      { key: 'from_name', value: 'Multi-Vendor eCommerce', type: 'string', category: 'email', description: 'From name' },

      // Payment Settings
      { key: 'stripe_publishable_key', value: '', type: 'string', category: 'payment', description: 'Stripe publishable key' },
      { key: 'stripe_secret_key', value: '', type: 'string', category: 'payment', description: 'Stripe secret key' },
      { key: 'paypal_client_id', value: '', type: 'string', category: 'payment', description: 'PayPal client ID' },
      { key: 'paypal_client_secret', value: '', type: 'string', category: 'payment', description: 'PayPal client secret' },
      { key: 'razorpay_key_id', value: process.env.RAZORPAY_KEY_ID || '', type: 'string', category: 'payment', description: 'Razorpay Key ID' },
      { key: 'razorpay_key_secret', value: process.env.RAZORPAY_KEY_SECRET || '', type: 'password', category: 'payment', description: 'Razorpay Secret Key' },

      // Security Settings
      { key: 'session_timeout', value: 30, type: 'number', category: 'security', description: 'Session timeout in minutes' },
      { key: 'password_min_length', value: 8, type: 'number', category: 'security', description: 'Minimum password length' },
      { key: 'max_login_attempts', value: 5, type: 'number', category: 'security', description: 'Maximum login attempts' },
      { key: 'account_lockout_time', value: 15, type: 'number', category: 'security', description: 'Account lockout time in minutes' },

      // Vendor Settings
      { key: 'vendor_commission_rate', value: 10, type: 'number', category: 'vendor', description: 'Default vendor commission rate (%)' },
      { key: 'vendor_auto_approval', value: false, type: 'boolean', category: 'vendor', description: 'Auto approve vendor registrations' },
      { key: 'product_auto_approval', value: false, type: 'boolean', category: 'vendor', description: 'Auto approve vendor products' },

      // Configuration (Environment Variables)
      { key: 'mongodb_uri', value: process.env.MONGODB_URI || '', type: 'password', category: 'configuration', description: 'MongoDB Connection URI' },
      { key: 'port', value: process.env.PORT || '8000', type: 'string', category: 'configuration', description: 'Server Port' },
      { key: 'node_env', value: process.env.NODE_ENV || 'development', type: 'string', category: 'configuration', description: 'Node Environment' },
      { key: 'frontend_url', value: process.env.FRONTEND_URL || 'http://localhost:3000', type: 'string', category: 'configuration', description: 'Frontend URL' },
      { key: 'app_name', value: process.env.APP_NAME || 'Alicartify', type: 'string', category: 'configuration', description: 'Application Name' },
      { key: 'jwt_secret', value: process.env.JWT_SECRET || '', type: 'password', category: 'configuration', description: 'JWT Secret Key' },
      { key: 'jwt_expires_in', value: process.env.JWT_EXPIRES_IN || '7d', type: 'string', category: 'configuration', description: 'JWT Expiration Time' },
      { key: 'jwt_refresh_secret', value: process.env.JWT_REFRESH_SECRET || '', type: 'password', category: 'configuration', description: 'JWT Refresh Secret' },
      { key: 'jwt_refresh_expires_in', value: process.env.JWT_REFRESH_EXPIRES_IN || '30d', type: 'string', category: 'configuration', description: 'JWT Refresh Expiration' },
      { key: 'session_secret', value: process.env.SESSION_SECRET || '', type: 'password', category: 'configuration', description: 'Session Secret' },
      { key: 'smtp_user', value: process.env.SMTP_USER || '', type: 'string', category: 'configuration', description: 'SMTP User Email' },
      { key: 'smtp_pass', value: process.env.SMTP_PASS || '', type: 'password', category: 'configuration', description: 'SMTP Password' },
      { key: 'smtp_from', value: process.env.SMTP_FROM || '', type: 'string', category: 'configuration', description: 'SMTP From Email' },
      { key: 'shiprocket_email', value: process.env.SHIPROCKET_EMAIL || '', type: 'string', category: 'configuration', description: 'Shiprocket Email' },
      { key: 'shiprocket_password', value: process.env.SHIPROCKET_PASSWORD || '', type: 'password', category: 'configuration', description: 'Shiprocket Password' },
      { key: 'cloudinary_cloud_name', value: process.env.CLOUDINARY_CLOUD_NAME || '', type: 'string', category: 'configuration', description: 'Cloudinary Cloud Name' },
      { key: 'cloudinary_api_key', value: process.env.CLOUDINARY_API_KEY || '', type: 'string', category: 'configuration', description: 'Cloudinary API Key' },
      { key: 'cloudinary_api_secret', value: process.env.CLOUDINARY_API_SECRET || '', type: 'password', category: 'configuration', description: 'Cloudinary API Secret' },
      { key: 'google_client_id', value: process.env.GOOGLE_CLIENT_ID || '', type: 'string', category: 'configuration', description: 'Google OAuth Client ID' },
      { key: 'google_client_secret', value: process.env.GOOGLE_CLIENT_SECRET || '', type: 'password', category: 'configuration', description: 'Google OAuth Client Secret' },
      { key: 'facebook_app_id', value: process.env.FACEBOOK_APP_ID || '', type: 'string', category: 'configuration', description: 'Facebook App ID' },
      { key: 'facebook_app_secret', value: process.env.FACEBOOK_APP_SECRET || '', type: 'password', category: 'configuration', description: 'Facebook App Secret' },
      { key: 'github_client_id', value: process.env.GITHUB_CLIENT_ID || '', type: 'string', category: 'configuration', description: 'GitHub Client ID' },
      { key: 'github_client_secret', value: process.env.GITHUB_CLIENT_SECRET || '', type: 'password', category: 'configuration', description: 'GitHub Client Secret' },
      { key: 'stripe_webhook_secret', value: process.env.STRIPE_WEBHOOK_SECRET || '', type: 'password', category: 'configuration', description: 'Stripe Webhook Secret' },
      { key: 'paypal_mode', value: process.env.PAYPAL_MODE || 'sandbox', type: 'string', category: 'configuration', description: 'PayPal Mode (sandbox/live)' },
      { key: 'aws_access_key_id', value: process.env.AWS_ACCESS_KEY_ID || '', type: 'string', category: 'configuration', description: 'AWS Access Key ID' },
      { key: 'aws_secret_access_key', value: process.env.AWS_SECRET_ACCESS_KEY || '', type: 'password', category: 'configuration', description: 'AWS Secret Access Key' },
      { key: 'aws_region', value: process.env.AWS_REGION || 'us-east-1', type: 'string', category: 'configuration', description: 'AWS Region' },
      { key: 'aws_s3_bucket', value: process.env.AWS_S3_BUCKET || '', type: 'string', category: 'configuration', description: 'AWS S3 Bucket Name' }
    ];

    const initPromises = defaultSettings.map(setting =>
      Setting.findOneAndUpdate(
        { key: setting.key },
        {
          ...setting,
          createdAt: new Date(),
          updatedAt: new Date(),
          updatedBy: req.user.userId
        },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true
        }
      )
    );

    await Promise.all(initPromises);

    res.json({
      success: true,
      message: 'Default settings initialized successfully'
    });

  } catch (error) {
    console.error('Initialize default settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize default settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get Shiprocket pickup locations
 */
const getShiprocketPickupLocations = async (req, res) => {
  try {
    const locations = await shiprocketService.fetchPickupLocations();

    res.json({
      success: true,
      data: locations,
      count: locations.length
    });

  } catch (error) {
    console.error('Get Shiprocket pickup locations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pickup locations',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Create default Shiprocket pickup location
 */
const createDefaultPickupLocation = async (req, res) => {
  try {
    const {
      pickupLocationName,
      address,
      city,
      state,
      pincode,
      phone,
      email
    } = req.body;

    // Validate required fields
    if (!pickupLocationName || !address || !city || !state || !pincode) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: pickupLocationName, address, city, state, pincode'
      });
    }

    // Temporarily set environment variables for default pickup creation
    const originalEnv = {
      SHIPROCKET_DEFAULT_PICKUP_NAME: process.env.SHIPROCKET_DEFAULT_PICKUP_NAME,
      SHIPROCKET_DEFAULT_ADDRESS: process.env.SHIPROCKET_DEFAULT_ADDRESS,
      SHIPROCKET_DEFAULT_CITY: process.env.SHIPROCKET_DEFAULT_CITY,
      SHIPROCKET_DEFAULT_STATE: process.env.SHIPROCKET_DEFAULT_STATE,
      SHIPROCKET_DEFAULT_PINCODE: process.env.SHIPROCKET_DEFAULT_PINCODE,
      SHIPROCKET_DEFAULT_PHONE: process.env.SHIPROCKET_DEFAULT_PHONE
    };

    // Set temporary values
    process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = pickupLocationName;
    process.env.SHIPROCKET_DEFAULT_ADDRESS = address;
    process.env.SHIPROCKET_DEFAULT_CITY = city;
    process.env.SHIPROCKET_DEFAULT_STATE = state;
    process.env.SHIPROCKET_DEFAULT_PINCODE = pincode;
    process.env.SHIPROCKET_DEFAULT_PHONE = phone || '';

    try {
      const result = await shiprocketService.createDefaultPickupLocation();

      res.json({
        success: true,
        message: 'Default pickup location created successfully',
        data: result
      });
    } finally {
      // Restore original environment variables
      Object.keys(originalEnv).forEach(key => {
        if (originalEnv[key] !== undefined) {
          process.env[key] = originalEnv[key];
        } else {
          delete process.env[key];
        }
      });
    }

  } catch (error) {
    console.error('Create default pickup location error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create default pickup location',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Test Shiprocket connection
 */
const testShiprocketConnection = async (req, res) => {
  try {
    // Try to authenticate and fetch pickup locations
    const locations = await shiprocketService.fetchPickupLocations();

    res.json({
      success: true,
      message: 'Shiprocket connection successful',
      data: {
        authenticated: true,
        pickupLocationsCount: locations.length,
        pickupLocations: locations.map(loc => ({
          name: loc.pickup_location,
          id: loc.id,
          address: `${loc.address}, ${loc.city}, ${loc.state} ${loc.pin_code}`
        }))
      }
    });

  } catch (error) {
    console.error('Test Shiprocket connection error:', error);
    res.status(500).json({
      success: false,
      message: 'Shiprocket connection failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      details: error.message
    });
  }
};

module.exports = {
  getSettings,
  getSettingsByCategory,
  updateSettings,
  updateSetting,
  deleteSetting,
  initializeDefaultSettings,
  getShiprocketPickupLocations,
  createDefaultPickupLocation,
  testShiprocketConnection
};