const ShiprocketService = require('../../src/services/shiprocketService');
const Vendor = require('../../src/models/Vendor');

// Mock the Vendor model
jest.mock('../../src/models/Vendor');

// Mock axios
jest.mock('axios');
const axios = require('axios');

describe('ShiprocketService Enhanced Validation', () => {
    let service;

    beforeEach(() => {
        service = new ShiprocketService();
        jest.clearAllMocks();

        // Mock successful authentication
        axios.post.mockResolvedValue({
            data: { token: 'mock-token' }
        });
    });

    describe('validateVendorForPickup', () => {
        test('should validate complete vendor address', () => {
            const vendor = {
                businessName: 'Test Business',
                businessAddress: {
                    street: '123 Test Street',
                    city: 'Mumbai',
                    state: 'Maharashtra',
                    zipCode: '400001'
                },
                contactInfo: {
                    businessPhone: '9876543210'
                }
            };

            const errors = service.validateVendorForPickup(vendor);
            expect(errors).toHaveLength(0);
        });

        test('should return errors for incomplete vendor address', () => {
            const vendor = {
                businessName: '',
                businessAddress: {
                    street: '',
                    city: 'Mumbai',
                    state: '',
                    zipCode: '400001'
                },
                contactInfo: {
                    businessPhone: ''
                }
            };

            const errors = service.validateVendorForPickup(vendor);
            expect(errors.length).toBeGreaterThan(0);
            expect(errors).toContain('Business name is required');
            expect(errors).toContain('Street address is required');
            expect(errors).toContain('State is required');
            expect(errors).toContain('Business phone number is required');
        });
    });

    describe('isVendorAddressComplete', () => {
        test('should return true for complete vendor address', () => {
            const vendor = {
                businessName: 'Test Business',
                businessAddress: {
                    street: '123 Test Street',
                    city: 'Mumbai',
                    state: 'Maharashtra',
                    zipCode: '400001'
                },
                contactInfo: {
                    businessPhone: '9876543210'
                }
            };

            expect(service.isVendorAddressComplete(vendor)).toBe(true);
        });

        test('should return false for incomplete vendor address', () => {
            const vendor = {
                businessName: 'Test Business',
                businessAddress: {
                    street: '123 Test Street',
                    city: 'Mumbai'
                    // missing state and zipCode
                },
                contactInfo: {
                    businessPhone: '9876543210'
                }
            };

            expect(service.isVendorAddressComplete(vendor)).toBe(false);
        });
    });

    describe('createOrder with validation', () => {
        test('should reject order with incomplete vendor address', async () => {
            const incompleteVendor = {
                _id: 'vendor123',
                businessName: 'Incomplete Vendor',
                businessAddress: {
                    street: '123 Test Street',
                    city: 'Mumbai'
                    // missing state and zipCode
                },
                contactInfo: {
                    businessPhone: '9876543210'
                }
            };

            Vendor.findById.mockResolvedValue(incompleteVendor);

            const orderData = {
                orderNumber: 'ORDER123',
                items: [{ vendor: 'vendor123', name: 'Test Item' }],
                billing: { firstName: 'John', lastName: 'Doe' },
                shipping: { firstName: 'John', lastName: 'Doe' }
            };

            await expect(service.createOrder(orderData)).rejects.toThrow(
                'Cannot process order for vendor Incomplete Vendor. Missing required address fields:'
            );
        });

        test('should process order with complete vendor address', async () => {
            const completeVendor = {
                _id: 'vendor123',
                businessName: 'Complete Vendor',
                businessAddress: {
                    street: '123 Test Street',
                    city: 'Mumbai',
                    state: 'Maharashtra',
                    zipCode: '400001'
                },
                contactInfo: {
                    businessPhone: '9876543210'
                }
            };

            Vendor.findById.mockResolvedValue(completeVendor);

            // Mock successful pickup location fetch
            axios.get.mockResolvedValue({
                data: { data: [{ pickup_location: 'Test Location' }] }
            });

            // Mock successful order creation
            axios.post.mockResolvedValueOnce({ data: { token: 'mock-token' } }); // auth
            axios.post.mockResolvedValueOnce({ data: { order_id: 'SR123' } }); // order creation

            const orderData = {
                orderNumber: 'ORDER123',
                items: [{
                    vendor: 'vendor123',
                    name: 'Test Item',
                    quantity: 1,
                    unitPrice: 100
                }],
                billing: {
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    phone: '9876543210',
                    address: {
                        street: '123 Billing St',
                        city: 'Mumbai',
                        state: 'Maharashtra',
                        zipCode: '400001',
                        country: 'India'
                    }
                },
                shipping: {
                    firstName: 'John',
                    lastName: 'Doe',
                    address: {
                        street: '123 Shipping St',
                        city: 'Mumbai',
                        state: 'Maharashtra',
                        zipCode: '400001',
                        country: 'India'
                    }
                },
                payment: { method: 'prepaid' },
                pricing: { subtotal: 100, shipping: 0 }
            };

            const result = await service.createOrder(orderData);
            expect(result.order_id).toBe('SR123');
        });
    });

    describe('createOrdersForVendors with validation', () => {
        test('should skip vendors with incomplete addresses', async () => {
            const incompleteVendor = {
                _id: 'vendor123',
                businessName: 'Incomplete Vendor',
                businessAddress: {
                    street: '123 Test Street',
                    city: 'Mumbai'
                    // missing state and zipCode
                },
                contactInfo: {
                    businessPhone: '9876543210'
                }
            };

            Vendor.findById.mockResolvedValue(incompleteVendor);

            const orderData = {
                orderNumber: 'ORDER123',
                items: [{ vendor: 'vendor123', name: 'Test Item' }],
                billing: { firstName: 'John', lastName: 'Doe' },
                shipping: { firstName: 'John', lastName: 'Doe' }
            };

            const results = await service.createOrdersForVendors(orderData);

            expect(results).toHaveLength(1);
            expect(results[0].status).toBe('incomplete_address');
            expect(results[0].error).toContain('Incomplete address for vendor');
            expect(results[0].missingFields).toBeDefined();
        });

        test('should process vendors with complete addresses', async () => {
            const completeVendor = {
                _id: 'vendor123',
                businessName: 'Complete Vendor',
                businessAddress: {
                    street: '123 Test Street',
                    city: 'Mumbai',
                    state: 'Maharashtra',
                    zipCode: '400001'
                },
                contactInfo: {
                    businessPhone: '9876543210',
                    businessEmail: '<EMAIL>'
                }
            };

            Vendor.findById.mockResolvedValue(completeVendor);

            // Mock successful pickup location fetch
            axios.get.mockResolvedValue({
                data: { data: [{ pickup_location: 'Test Location' }] }
            });

            // Mock successful order creation
            axios.post.mockResolvedValueOnce({ data: { token: 'mock-token' } }); // auth
            axios.post.mockResolvedValueOnce({ data: { order_id: 'SR123' } }); // order creation

            const orderData = {
                orderNumber: 'ORDER123',
                items: [{
                    vendor: 'vendor123',
                    name: 'Test Item',
                    quantity: 1,
                    unitPrice: 100
                }],
                billing: {
                    firstName: 'John',
                    lastName: 'Doe',
                    email: '<EMAIL>',
                    phone: '9876543210',
                    address: {
                        street: '123 Billing St',
                        city: 'Mumbai',
                        state: 'Maharashtra',
                        zipCode: '400001',
                        country: 'India'
                    }
                },
                shipping: {
                    firstName: 'John',
                    lastName: 'Doe',
                    address: {
                        street: '123 Shipping St',
                        city: 'Mumbai',
                        state: 'Maharashtra',
                        zipCode: '400001',
                        country: 'India'
                    }
                },
                payment: { method: 'prepaid' },
                pricing: { subtotal: 100, shipping: 0 }
            };

            const results = await service.createOrdersForVendors(orderData);

            expect(results).toHaveLength(1);
            expect(results[0].response.order_id).toBe('SR123');
            expect(results[0].vendorName).toBe('Complete Vendor');
        });
    });

    describe('getMissingVendorFields', () => {
        test('should return missing fields for incomplete vendor', () => {
            const vendor = {
                businessName: 'Test Business',
                businessAddress: {
                    street: '123 Test Street',
                    city: 'Mumbai'
                    // missing state and zipCode
                },
                contactInfo: {
                    // missing businessPhone
                    businessEmail: '<EMAIL>'
                }
            };

            const missing = service.getMissingVendorFields(vendor);

            expect(missing).toContain('businessAddress.state');
            expect(missing).toContain('businessAddress.zipCode');
            expect(missing).toContain('contactInfo.businessPhone');
        });
    });
});