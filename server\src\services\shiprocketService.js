const axios = require('axios');
const Vendor = require('../models/Vendor');
const VendorAddressValidator = require('../utils/VendorAddressValidator');

class ShiprocketService {
  constructor() {
    this.baseURL = 'https://apiv2.shiprocket.in/v1/external';
    this.token = null;
    this.tokenExpiry = null;
    this.addressValidator = new VendorAddressValidator();
  }

  // Get authentication token
  async authenticate() {
    try {
      const email = String(process.env.SHIPROCKET_EMAIL || '').trim();
      const password = String(process.env.SHIPROCKET_PASSWORD || '').trim();

      if (!email || !password) {
        throw new Error('Shiprocket email and password must be configured in environment variables');
      }

      console.log(`🔐 Authenticating with Shiprocket using email: ${email}`);

      const response = await axios.post(`${this.baseURL}/auth/login`, {
        email,
        password
      });

      this.token = response.data.token;
      this.tokenExpiry = new Date(Date.now() + (9 * 24 * 60 * 60 * 1000));
      
      console.log(`✅ Successfully authenticated with Shiprocket`);
      return this.token;
    } catch (error) {
      console.error('❌ Shiprocket authentication failed:', {
        status: error.response?.status,
        message: error.response?.data?.message || error.message,
        email: process.env.SHIPROCKET_EMAIL ? 'configured' : 'missing'
      });
      throw new Error(`Failed to authenticate with Shiprocket: ${error.response?.data?.message || error.message}`);
    }
  }

  // Get headers with authentication
  async getAuthHeaders() {
    if (!this.token || new Date() >= this.tokenExpiry) {
      await this.authenticate();
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
  }

  async fetchPickupLocations() {
    const headers = await this.getAuthHeaders();
    const response = await axios.get(`${this.baseURL}/settings/company/pickup`, { headers });
    const list = Array.isArray(response.data?.data) ? response.data.data : [];
    return list;
  }

  // Validate vendor data for pickup location creation (enhanced)
  validateVendorForPickup(vendor) {
    const validation = this.addressValidator.validateVendorAddress(vendor);
    
    // Log validation results
    const message = this.addressValidator.formatValidationMessage(validation, vendor?.businessName);
    console.log(message);
    
    // Log warnings separately if any
    if (validation.warnings.length > 0) {
      console.log(`⚠️  Address validation warnings for ${vendor?.businessName || 'Unknown'}:`);
      validation.warnings.forEach(warning => console.log(`   - ${warning}`));
    }
    
    return validation.errors;
  }

  // Check if vendor address is complete before processing orders
  isVendorAddressComplete(vendor) {
    return this.addressValidator.isAddressComplete(vendor);
  }

  // Get missing required fields for vendor
  getMissingVendorFields(vendor) {
    return this.addressValidator.getMissingRequiredFields(vendor);
  }

  // Get full validation status for vendor address
  getVendorAddressValidation(vendor) {
    return this.addressValidator.validateVendorAddress(vendor);
  }

  // Validate all vendors in an order before processing
  validateOrderVendors(orderData) {
    const validationResults = [];
    const vendorIds = [...new Set(orderData.items.map(item => item.vendor))];

    for (const vendorId of vendorIds) {
      validationResults.push({
        vendorId,
        needsValidation: true
      });
    }

    return validationResults;
  }

  // Get configuration status for default pickup location
  getDefaultPickupLocationStatus() {
    const config = this.validateDefaultPickupConfig();
    const hasBasicConfig = !!process.env.SHIPROCKET_DEFAULT_PICKUP_NAME;
    
    return {
      hasBasicConfig,
      hasCompleteConfig: config.isValid,
      configErrors: config.errors,
      configWarnings: config.warnings,
      recommendedAction: this.getRecommendedConfigAction(hasBasicConfig, config.isValid)
    };
  }

  // Get recommended action for pickup location configuration
  getRecommendedConfigAction(hasBasic, hasComplete) {
    if (!hasBasic) {
      return 'Set SHIPROCKET_DEFAULT_PICKUP_NAME to match an existing pickup location in your Shiprocket dashboard';
    }
    
    if (!hasComplete) {
      return 'Either manually create the pickup location in Shiprocket dashboard, or set complete address environment variables for auto-creation';
    }
    
    return 'Configuration is complete - system can auto-create pickup locations if needed';
  }

  // Test the default pickup location configuration
  async testDefaultPickupLocationConfig() {
    console.log(`🧪 Testing default pickup location configuration...`);
    
    const status = this.getDefaultPickupLocationStatus();
    
    console.log(`📊 Configuration Status:`);
    console.log(`   Basic Config (SHIPROCKET_DEFAULT_PICKUP_NAME): ${status.hasBasicConfig ? '✅' : '❌'}`);
    console.log(`   Complete Config (for auto-creation): ${status.hasCompleteConfig ? '✅' : '❌'}`);
    
    if (status.configErrors.length > 0) {
      console.log(`❌ Configuration Errors:`);
      status.configErrors.forEach(error => console.log(`   - ${error}`));
    }
    
    if (status.configWarnings.length > 0) {
      console.log(`⚠️  Configuration Warnings:`);
      status.configWarnings.forEach(warning => console.log(`   - ${warning}`));
    }
    
    console.log(`💡 Recommended Action: ${status.recommendedAction}`);
    
    // Test if we can fetch existing pickup locations
    try {
      const locations = await this.fetchPickupLocations();
      console.log(`📍 Found ${locations.length} existing pickup location(s) in Shiprocket`);
      
      if (locations.length > 0) {
        console.log(`   Available locations: ${locations.map(l => l.pickup_location).join(', ')}`);
        
        // Check if default name matches any existing location
        if (process.env.SHIPROCKET_DEFAULT_PICKUP_NAME) {
          const matches = locations.some(l => 
            l.pickup_location.toLowerCase() === process.env.SHIPROCKET_DEFAULT_PICKUP_NAME.toLowerCase()
          );
          console.log(`   Default name match: ${matches ? '✅' : '❌'}`);
        }
      }
      
    } catch (error) {
      console.log(`❌ Could not fetch pickup locations: ${error.message}`);
    }
    
    return status;
  }

  async addPickupLocationForVendor(vendor) {
    // Validate vendor data
    const validationErrors = this.validateVendorForPickup(vendor);
    if (validationErrors.length > 0) {
      throw new Error(`Cannot create pickup location for vendor ${vendor?.businessName || 'Unknown'}: ${validationErrors.join(', ')}`);
    }

    const headers = await this.getAuthHeaders();

    // Create unique pickup location name to avoid conflicts
    const baseLocationName = String(vendor.businessName || 'Vendor').trim();
    const uniqueLocationName = `${baseLocationName}-${vendor._id.toString().slice(-6)}`;

    const payload = {
      pickup_location: uniqueLocationName,
      name: String(vendor.businessName || '').trim(),
      email: String(vendor.contactInfo?.businessEmail || process.env.SHIPROCKET_EMAIL || '').trim(),
      phone: String(vendor.contactInfo?.businessPhone || '').trim(),
      address: String(vendor.businessAddress?.street || '').trim(),
      address_2: '',
      city: String(vendor.businessAddress?.city || '').trim(),
      state: String(vendor.businessAddress?.state || '').trim(),
      country: String(vendor.businessAddress?.country || 'India').trim(),
      pin_code: String(vendor.businessAddress?.zipCode || '').trim()
    };

    console.log(`Creating Shiprocket pickup location for vendor ${vendor.businessName}:`, payload);

    try {
      const response = await axios.post(`${this.baseURL}/settings/company/addpickup`, payload, { headers });

      // Persist the configured name and code to vendor
      vendor.shiprocket = vendor.shiprocket || {};
      vendor.shiprocket.pickupLocationName = uniqueLocationName;
      vendor.shiprocket.pickupLocationCode = response.data?.pickup_id || null;
      await vendor.save();

      console.log(`✅ Successfully created pickup location for vendor ${vendor.businessName}: ${uniqueLocationName}`);
      return response.data;
    } catch (error) {
      const statusCode = error.response?.status;
      const errorMessage = error.response?.data?.message || error.message;
      
      console.error(`❌ Failed to create pickup location for vendor ${vendor.businessName}:`, {
        status: statusCode,
        message: errorMessage,
        vendorId: vendor._id,
        pickupLocationName: uniqueLocationName
      });

      // Provide specific error context for debugging
      if (statusCode === 403) {
        console.log(`🔒 Unauthorized access - Shiprocket account may lack permissions to create pickup locations`);
        console.log(`📝 Vendor details for manual setup:`);
        console.log(`   Business Name: ${vendor.businessName}`);
        console.log(`   Address: ${vendor.businessAddress?.street}, ${vendor.businessAddress?.city}`);
        console.log(`   State: ${vendor.businessAddress?.state}, PIN: ${vendor.businessAddress?.zipCode}`);
        console.log(`   Phone: ${vendor.contactInfo?.businessPhone}`);
      }
      
      // Re-throw with original error for upstream handling
      throw error;
    }
  }

  async resolvePickupLocationName(preferredName, vendor) {
    console.log(`🔍 Resolving pickup location for vendor: ${vendor?.businessName || 'Unknown'}`);

    try {
      const locations = await this.fetchPickupLocations();
      const names = new Set(locations.map(loc => String(loc.pickup_location || '').trim()).filter(Boolean));

      console.log(`📍 Available pickup locations: [${Array.from(names).join(', ')}]`);

      // Priority 1: Check if vendor already has a configured pickup location
      if (vendor?.shiprocket?.pickupLocationName) {
        const vendorPickupName = String(vendor.shiprocket.pickupLocationName).trim();
        if (names.has(vendorPickupName)) {
          console.log(`✅ Using existing vendor pickup location: ${vendorPickupName}`);
          return vendorPickupName;
        } else {
          console.log(`⚠️  Vendor's configured pickup location '${vendorPickupName}' not found in Shiprocket`);
        }
      }

      // Priority 2: Check if vendor business name matches existing location
      const vendorName = vendor ? String(vendor.businessName || '').trim() : '';
      if (vendorName && names.has(vendorName)) {
        console.log(`✅ Using vendor business name as pickup location: ${vendorName}`);
        return vendorName;
      }

      // Priority 3: Check environment variable preference
      const envName = String(preferredName || '').trim();
      if (envName && names.has(envName)) {
        console.log(`✅ Using environment preferred pickup location: ${envName}`);
        return envName;
      }

      // Priority 4: Auto-create pickup location for vendor (if permissions allow)
      if (vendor && vendorName) {
        console.log(`🔧 Attempting to create new pickup location for vendor: ${vendorName}`);
        try {
          const createResult = await this.addPickupLocationForVendor(vendor);

          // Re-fetch locations to get the newly created one
          const updatedLocations = await this.fetchPickupLocations();
          const updatedNames = new Set(updatedLocations.map(loc => String(loc.pickup_location || '').trim()).filter(Boolean));

          // Check for the unique location name we created
          const uniqueLocationName = `${vendorName}-${vendor._id.toString().slice(-6)}`;
          if (updatedNames.has(uniqueLocationName)) {
            console.log(`✅ Successfully created and using new pickup location: ${uniqueLocationName}`);
            return uniqueLocationName;
          }

          console.log(`⚠️  Created pickup location but couldn't find it in updated list`);
        } catch (createError) {
          console.error(`❌ Failed to create pickup location for vendor ${vendorName}:`, createError.message);
          
          // Handle pickup location creation failure gracefully
          const fallbackLocation = await this.handlePickupLocationFailure(vendor, createError);
          if (fallbackLocation) {
            return fallbackLocation;
          }
        }
      }

      // Priority 5: Fallback to first available pickup location
      const first = locations.find(l => l && l.pickup_location);
      if (first?.pickup_location) {
        const fallbackName = String(first.pickup_location);
        console.log(`⚠️  Using fallback pickup location: ${fallbackName}`);
        console.log(`📝 Note: This may not be the correct pickup location for vendor ${vendorName}`);
        console.log(`💡 Consider manually creating a pickup location in Shiprocket dashboard for this vendor`);
        return fallbackName;
      }

      // Priority 6: Try to ensure at least one pickup location exists
      console.log(`🚨 No pickup locations found in Shiprocket account`);
      console.log(`🔧 Attempting to get or create a default pickup location...`);

      try {
        const defaultPickupName = await this.getOrCreateDefaultPickupLocation();
        console.log(`✅ Using default pickup location: ${defaultPickupName}`);
        console.log(`📝 Note: This is a default location, not vendor-specific`);
        return defaultPickupName;
      } catch (defaultError) {
        console.error(`❌ Failed to get default pickup location:`, defaultError.message);

        // Final fallback - provide detailed instructions
        console.log(`📝 Manual setup required. Please create a pickup location in your Shiprocket dashboard:`);
        console.log(`   1. Login to Shiprocket dashboard (https://app.shiprocket.in)`);
        console.log(`   2. Go to Settings > Company Profile > Pickup Locations`);
        console.log(`   3. Click "Add Pickup Location"`);
        console.log(`   4. Add your business address details`);
        console.log(`   5. Save the pickup location`);

        if (vendor && vendor.businessAddress) {
          console.log(`📍 Suggested address for vendor ${vendorName}:`);
          console.log(`   Address: ${vendor.businessAddress.street}`);
          console.log(`   City: ${vendor.businessAddress.city}`);
          console.log(`   State: ${vendor.businessAddress.state}`);
          console.log(`   PIN: ${vendor.businessAddress.zipCode}`);
          console.log(`   Phone: ${vendor.contactInfo?.businessPhone || 'N/A'}`);
        }

        throw new Error('No pickup locations configured in Shiprocket and unable to create default location. Please manually add a pickup location in your Shiprocket dashboard.');
      }

    } catch (error) {
      console.error('❌ Error in resolvePickupLocationName:', error.message);
      throw error;
    }
  }

  // Create order with vendor's address as pickup (single vendor order)
  async createOrder(orderData) {
    console.log(`🚀 Creating single Shiprocket order: ${orderData.orderNumber}`);

    try {
      const headers = await this.getAuthHeaders();

      // Get first vendor from items to use their address
      const firstItem = orderData.items[0];
      if (!firstItem || !firstItem.vendor) {
        throw new Error('No vendor found in order items');
      }

      const vendor = await Vendor.findById(firstItem.vendor);
      if (!vendor) {
        throw new Error(`Vendor not found: ${firstItem.vendor}`);
      }

      console.log(`📋 Processing single order for vendor: ${vendor.businessName}`);

      // Validate vendor address before processing
      if (!this.isVendorAddressComplete(vendor)) {
        const missingFields = this.getMissingVendorFields(vendor);
        const errorMsg = `Cannot process order for vendor ${vendor.businessName}. Missing required address fields: ${missingFields.join(', ')}`;
        console.error(`❌ ${errorMsg}`);
        throw new Error(errorMsg);
      }

      // Resolve a valid pickup location name from Shiprocket account
      const preferredName = (vendor?.shiprocket?.pickupLocationName) || (vendor?.businessName) || process.env.SHIPROCKET_PICKUP_LOCATION;
      const pickupLocationName = await this.resolvePickupLocationName(preferredName, vendor);

      const shiprocketOrder = {
        order_id: orderData.orderNumber,
        order_date: new Date().toISOString().split('T')[0],
        pickup_location: pickupLocationName,
        billing_customer_name: `${orderData.billing.firstName} ${orderData.billing.lastName}`,
        billing_address: orderData.billing.address.street,
        billing_city: orderData.billing.address.city,
        billing_pincode: String(orderData.billing.address.zipCode),
        billing_state: orderData.billing.address.state,
        billing_country: orderData.billing.address.country || 'India',
        billing_email: orderData.billing.email,
        billing_phone: String(orderData.billing.phone),
        shipping_is_billing: false,
        shipping_customer_name: `${orderData.shipping.firstName} ${orderData.shipping.lastName}`,
        shipping_address: orderData.shipping.address.street,
        shipping_city: orderData.shipping.address.city,
        shipping_pincode: String(orderData.shipping.address.zipCode),
        shipping_state: orderData.shipping.address.state,
        shipping_country: orderData.shipping.address.country || 'India',
        shipping_phone: String(orderData.shipping.phone || orderData.billing.phone),
        order_items: orderData.items.map(item => ({
          name: item.name,
          sku: item.sku,
          units: item.quantity,
          selling_price: parseFloat(item.unitPrice || item.totalPrice / item.quantity),
        })),
        payment_method: orderData.payment.method === 'cod' ? 'COD' : 'Prepaid',
        shipping_charges: parseFloat(orderData.pricing.shipping || 0),
        sub_total: parseFloat(orderData.pricing.subtotal),
        length: 10,
        breadth: 10,
        height: 10,
        weight: 0.5
      };

      console.log(`📤 Creating Shiprocket order with pickup location: ${pickupLocationName}`);
      const response = await axios.post(`${this.baseURL}/orders/create/adhoc`, shiprocketOrder, { headers });

      console.log(`✅ Successfully created Shiprocket order: ${response.data?.order_id}`);
      return response.data;

    } catch (error) {
      console.error('❌ Failed to create Shiprocket order:', error.response?.data || error.message);
      throw new Error(`Failed to create order in Shiprocket: ${error.response?.data?.message || error.message}`);
    }
  }

  // Create separate Shiprocket orders per vendor using each vendor's pickup location
  async createOrdersForVendors(orderData) {
    console.log(`🚀 Creating Shiprocket orders for order: ${orderData.orderNumber}`);

    try {
      const headers = await this.getAuthHeaders();

      // Group items by vendor
      const vendorToItemsMap = new Map();
      for (const item of orderData.items) {
        const vendorId = String(item.vendor);
        if (!vendorToItemsMap.has(vendorId)) vendorToItemsMap.set(vendorId, []);
        vendorToItemsMap.get(vendorId).push(item);
      }

      console.log(`📦 Processing ${vendorToItemsMap.size} vendor(s) for order ${orderData.orderNumber}`);

      const results = [];
      let vendorIndex = 0;

      for (const [vendorId, vendorItems] of vendorToItemsMap.entries()) {
        vendorIndex += 1;
        console.log(`\n🏪 Processing vendor ${vendorIndex}/${vendorToItemsMap.size}: ${vendorId}`);

        try {
          const vendor = await Vendor.findById(vendorId);
          if (!vendor) {
            const error = `Vendor not found: ${vendorId}`;
            console.error(`❌ ${error}`);
            results.push({ vendorId, error, status: 'vendor_not_found' });
            continue;
          }

          console.log(`📋 Vendor details: ${vendor.businessName} (${vendor.contactInfo?.businessEmail})`);

          // Validate vendor address before processing
          if (!this.isVendorAddressComplete(vendor)) {
            const missingFields = this.getMissingVendorFields(vendor);
            const error = `Incomplete address for vendor ${vendor.businessName}. Missing: ${missingFields.join(', ')}`;
            console.error(`❌ ${error}`);
            results.push({ 
              vendorId, 
              vendorName: vendor.businessName,
              error, 
              status: 'incomplete_address',
              missingFields 
            });
            continue;
          }

          // Resolve pickup location for this specific vendor
          const preferredName = (vendor?.shiprocket?.pickupLocationName) || (vendor?.businessName) || process.env.SHIPROCKET_PICKUP_LOCATION;
          const pickupLocationName = await this.resolvePickupLocationName(preferredName, vendor);

          // Ensure unique order_id per vendor
          const vendorOrderId = `${orderData.orderNumber}-V${vendorIndex}`;

          const shiprocketOrder = {
            order_id: vendorOrderId,
            order_date: new Date().toISOString().split('T')[0],
            pickup_location: pickupLocationName,
            billing_customer_name: `${orderData.billing.firstName} ${orderData.billing.lastName}`,
            billing_address: orderData.billing.address.street,
            billing_city: orderData.billing.address.city,
            billing_pincode: String(orderData.billing.address.zipCode),
            billing_state: orderData.billing.address.state,
            billing_country: orderData.billing.address.country || 'India',
            billing_email: orderData.billing.email,
            billing_phone: String(orderData.billing.phone),
            shipping_is_billing: false,
            shipping_customer_name: `${orderData.shipping.firstName} ${orderData.shipping.lastName}`,
            shipping_address: orderData.shipping.address.street,
            shipping_city: orderData.shipping.address.city,
            shipping_pincode: String(orderData.shipping.address.zipCode),
            shipping_state: orderData.shipping.address.state,
            shipping_country: orderData.shipping.address.country || 'India',
            shipping_phone: String(orderData.shipping.phone || orderData.billing.phone),
            order_items: vendorItems.map(item => ({
              name: item.name,
              sku: item.sku,
              units: item.quantity,
              selling_price: parseFloat(item.unitPrice || item.totalPrice / item.quantity),
            })),
            payment_method: orderData.payment.method === 'cod' ? 'COD' : 'Prepaid',
            shipping_charges: 0,
            sub_total: parseFloat(vendorItems.reduce((acc, it) => acc + (it.unitPrice || (it.totalPrice || 0) / Math.max(1, it.quantity)), 0)),
            length: 10,
            breadth: 10,
            height: 10,
            weight: 0.5
          };

          console.log(`📤 Creating Shiprocket order for vendor ${vendor.businessName} with pickup location: ${pickupLocationName}`);

          const response = await axios.post(`${this.baseURL}/orders/create/adhoc`, shiprocketOrder, { headers });

          console.log(`✅ Successfully created Shiprocket order for vendor ${vendor.businessName}: ${response.data?.order_id}`);
          results.push({
            vendorId,
            vendorName: vendor.businessName,
            pickupLocation: pickupLocationName,
            response: response.data
          });

        } catch (error) {
          const errMsg = error.response?.data?.message || error.message;
          console.error(`❌ Failed to create Shiprocket order for vendor ${vendorId}:`, error.response?.data || error.message);
          results.push({
            vendorId,
            pickupLocation: null,
            error: errMsg,
            status: error.response?.status || 'unknown_error'
          });
        }
      }

      console.log(`\n📊 Shiprocket order creation summary: ${results.filter(r => !r.error).length}/${results.length} successful`);
      return results;

    } catch (error) {
      console.error('❌ Critical error in createOrdersForVendors:', error.response?.data || error.message);
      throw new Error(`Failed to create Shiprocket orders: ${error.response?.data?.message || error.message}`);
    }
  }
  // Create a default pickup location using environment variables with enhanced validation
  async createDefaultPickupLocation() {
    try {
      console.log(`🏗️  Creating default pickup location...`);
      
      // Validate required environment variables
      const validationResult = this.validateDefaultPickupConfig();
      if (!validationResult.isValid) {
        throw new Error(`Cannot create default pickup location: ${validationResult.errors.join(', ')}`);
      }
      
      const headers = await this.getAuthHeaders();

      const payload = {
        pickup_location: process.env.SHIPROCKET_DEFAULT_PICKUP_NAME || 'Main Warehouse',
        name: process.env.SHIPROCKET_DEFAULT_PICKUP_NAME || 'Main Warehouse',
        email: process.env.SHIPROCKET_EMAIL || process.env.SHIPROCKET_DEFAULT_EMAIL || '',
        phone: process.env.SHIPROCKET_DEFAULT_PHONE || '',
        address: process.env.SHIPROCKET_DEFAULT_ADDRESS || '',
        address_2: process.env.SHIPROCKET_DEFAULT_ADDRESS_2 || '',
        city: process.env.SHIPROCKET_DEFAULT_CITY || '',
        state: process.env.SHIPROCKET_DEFAULT_STATE || '',
        country: process.env.SHIPROCKET_DEFAULT_COUNTRY || 'India',
        pin_code: process.env.SHIPROCKET_DEFAULT_PINCODE || ''
      };

      console.log(`📦 Default pickup location payload:`, {
        pickup_location: payload.pickup_location,
        name: payload.name,
        city: payload.city,
        state: payload.state,
        pin_code: payload.pin_code
      });

      const response = await axios.post(`${this.baseURL}/settings/company/addpickup`, payload, { headers });
      
      console.log(`✅ Successfully created default pickup location: ${payload.pickup_location}`);
      return response.data;

    } catch (error) {
      const statusCode = error.response?.status;
      const errorMessage = error.response?.data?.message || error.message;
      
      console.error(`❌ Failed to create default pickup location:`, {
        status: statusCode,
        message: errorMessage
      });

      // Provide specific guidance based on error type
      if (statusCode === 403) {
        console.log(`🔒 Unauthorized: Shiprocket account lacks permissions to create pickup locations`);
        console.log(`📝 Please manually create a pickup location in Shiprocket dashboard:`);
        console.log(`   Location Name: ${process.env.SHIPROCKET_DEFAULT_PICKUP_NAME || 'Main Warehouse'}`);
        console.log(`   Address: ${process.env.SHIPROCKET_DEFAULT_ADDRESS}`);
        console.log(`   City: ${process.env.SHIPROCKET_DEFAULT_CITY}`);
        console.log(`   State: ${process.env.SHIPROCKET_DEFAULT_STATE}`);
        console.log(`   PIN: ${process.env.SHIPROCKET_DEFAULT_PINCODE}`);
      } else if (statusCode === 400) {
        console.log(`📝 Bad request: Please check your default pickup location configuration`);
      }
      
      throw new Error(`Failed to create default pickup location: ${errorMessage}`);
    }
  }

  // Validate default pickup location configuration
  validateDefaultPickupConfig() {
    const errors = [];
    const warnings = [];

    // Required fields
    const requiredFields = [
      { env: 'SHIPROCKET_DEFAULT_ADDRESS', name: 'Default address' },
      { env: 'SHIPROCKET_DEFAULT_CITY', name: 'Default city' },
      { env: 'SHIPROCKET_DEFAULT_STATE', name: 'Default state' },
      { env: 'SHIPROCKET_DEFAULT_PINCODE', name: 'Default PIN code' }
    ];

    requiredFields.forEach(({ env, name }) => {
      if (!process.env[env] || process.env[env].trim() === '') {
        errors.push(`${name} (${env}) is required`);
      }
    });

    // Validate PIN code format
    if (process.env.SHIPROCKET_DEFAULT_PINCODE) {
      const pincode = process.env.SHIPROCKET_DEFAULT_PINCODE.trim();
      if (!/^[1-9][0-9]{5}$/.test(pincode)) {
        errors.push('Default PIN code must be a valid 6-digit Indian PIN code');
      }
    }

    // Validate phone number if provided
    if (process.env.SHIPROCKET_DEFAULT_PHONE) {
      const phone = process.env.SHIPROCKET_DEFAULT_PHONE.replace(/\D/g, '');
      if (!/^[6-9]\d{9}$/.test(phone) && !/^91[6-9]\d{9}$/.test(phone)) {
        warnings.push('Default phone number should be a valid Indian mobile number');
      }
    } else {
      warnings.push('Default phone number is recommended');
    }

    // Check pickup location name
    if (!process.env.SHIPROCKET_DEFAULT_PICKUP_NAME) {
      warnings.push('SHIPROCKET_DEFAULT_PICKUP_NAME not set, will use "Main Warehouse"');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Handle pickup location creation failures gracefully
  async handlePickupLocationFailure(vendor, error) {
    const isUnauthorized = error.response?.status === 403 || 
                          error.message.includes('Unauthorized') || 
                          error.message.includes('permissions') ||
                          error.message.includes('403');

    if (isUnauthorized) {
      console.log(`🔒 Cannot create pickup location for ${vendor.businessName} - unauthorized access`);
      console.log(`📝 Using existing pickup locations instead`);
      
      // Try to use the first available pickup location
      try {
        const locations = await this.fetchPickupLocations();
        if (locations.length > 0) {
          const fallbackLocation = locations[0].pickup_location;
          console.log(`⚠️  Using fallback pickup location: ${fallbackLocation}`);
          console.log(`📍 Note: This may not be the correct pickup location for vendor ${vendor.businessName}`);
          return fallbackLocation;
        }
      } catch (fetchError) {
        console.error(`❌ Failed to fetch existing pickup locations:`, fetchError.message);
      }
      
      // If no existing locations, try to create default
      return await this.getOrCreateDefaultPickupLocation();
    }
    
    // For other errors, log and continue with fallback
    console.error(`❌ Pickup location creation failed for ${vendor.businessName}:`, error.message);
    return await this.getOrCreateDefaultPickupLocation();
  }

  // Get or create a default pickup location with enhanced fallback strategies
  async getOrCreateDefaultPickupLocation() {
    try {
      console.log(`🔍 Searching for available pickup locations...`);
      const locations = await this.fetchPickupLocations();
      
      if (locations.length > 0) {
        // Prefer locations that match default naming patterns
        const defaultLocation = this.findBestDefaultLocation(locations);
        console.log(`✅ Using existing pickup location: ${defaultLocation}`);
        return defaultLocation;
      }
      
      console.log(`⚠️  No existing pickup locations found in Shiprocket account`);
      
      // Try multiple fallback strategies
      return await this.tryFallbackStrategies();
      
    } catch (error) {
      console.error(`❌ Error in getOrCreateDefaultPickupLocation:`, error.message);
      
      // Final emergency fallback
      return await this.getEmergencyFallback();
    }
  }

  // Find the best default location from available locations
  findBestDefaultLocation(locations) {
    const defaultName = process.env.SHIPROCKET_DEFAULT_PICKUP_NAME;
    
    // Strategy 1: Exact match with environment variable
    if (defaultName) {
      const exactMatch = locations.find(loc => 
        loc.pickup_location && loc.pickup_location.toLowerCase() === defaultName.toLowerCase()
      );
      if (exactMatch) {
        console.log(`🎯 Found exact match for default pickup location: ${exactMatch.pickup_location}`);
        return exactMatch.pickup_location;
      }
    }
    
    // Strategy 2: Look for common default names
    const commonDefaults = ['main warehouse', 'warehouse', 'main', 'default', 'head office', 'hq'];
    for (const commonName of commonDefaults) {
      const match = locations.find(loc => 
        loc.pickup_location && loc.pickup_location.toLowerCase().includes(commonName)
      );
      if (match) {
        console.log(`🏢 Found common default location: ${match.pickup_location}`);
        return match.pickup_location;
      }
    }
    
    // Strategy 3: Use the first available location
    const firstLocation = locations[0].pickup_location;
    console.log(`📍 Using first available pickup location: ${firstLocation}`);
    return firstLocation;
  }

  // Try multiple fallback strategies when no pickup locations exist
  async tryFallbackStrategies() {
    console.log(`🔧 Trying fallback strategies for pickup location...`);
    
    // Strategy 1: Use environment variable as-is (assume it exists in Shiprocket)
    if (process.env.SHIPROCKET_DEFAULT_PICKUP_NAME) {
      console.log(`📝 Using environment default pickup location: ${process.env.SHIPROCKET_DEFAULT_PICKUP_NAME}`);
      console.log(`⚠️  Note: This location may not exist in Shiprocket - orders may fail if not manually created`);
      return process.env.SHIPROCKET_DEFAULT_PICKUP_NAME;
    }
    
    // Strategy 2: Try to create a default pickup location if we have all required data
    if (this.hasCompleteDefaultConfig()) {
      console.log(`🏗️  Attempting to create default pickup location...`);
      try {
        await this.createDefaultPickupLocation();
        
        // Re-fetch to confirm creation
        const updatedLocations = await this.fetchPickupLocations();
        if (updatedLocations.length > 0) {
          const newLocation = updatedLocations[0].pickup_location;
          console.log(`✅ Successfully created default pickup location: ${newLocation}`);
          return newLocation;
        }
      } catch (createError) {
        console.log(`⚠️  Could not create default pickup location: ${createError.message}`);
        // Continue to next strategy
      }
    }
    
    // Strategy 3: Use a generic fallback name
    const genericFallback = 'Main Warehouse';
    console.log(`🏪 Using generic fallback pickup location: ${genericFallback}`);
    console.log(`⚠️  This is a placeholder - please create this location in Shiprocket dashboard`);
    return genericFallback;
  }

  // Check if we have complete configuration for creating a default pickup location
  hasCompleteDefaultConfig() {
    const requiredVars = [
      'SHIPROCKET_DEFAULT_ADDRESS',
      'SHIPROCKET_DEFAULT_CITY', 
      'SHIPROCKET_DEFAULT_STATE',
      'SHIPROCKET_DEFAULT_PINCODE'
    ];
    
    const missing = requiredVars.filter(varName => !process.env[varName]);
    
    if (missing.length > 0) {
      console.log(`📝 Cannot auto-create default pickup location. Missing environment variables: ${missing.join(', ')}`);
      return false;
    }
    
    return true;
  }

  // Emergency fallback when all else fails
  async getEmergencyFallback() {
    console.log(`🚨 Emergency fallback activated - all pickup location strategies failed`);
    
    // Try one more time to fetch existing locations (network might have been down)
    try {
      const locations = await this.fetchPickupLocations();
      if (locations.length > 0) {
        const emergencyLocation = locations[0].pickup_location;
        console.log(`🆘 Emergency: Using any available pickup location: ${emergencyLocation}`);
        return emergencyLocation;
      }
    } catch (fetchError) {
      console.log(`❌ Emergency fetch also failed: ${fetchError.message}`);
    }
    
    // Absolute last resort - use environment variable or generic name
    const lastResort = process.env.SHIPROCKET_DEFAULT_PICKUP_NAME || 'Emergency Warehouse';
    console.log(`🆘 Last resort pickup location: ${lastResort}`);
    console.log(`📞 URGENT: Please manually create this pickup location in Shiprocket dashboard immediately`);
    console.log(`🔗 Shiprocket Dashboard: https://app.shiprocket.in/settings/company-profile`);
    
    return lastResort;
  }

  // Get or create a fallback pickup location (legacy method - use getOrCreateDefaultPickupLocation instead)
  async ensurePickupLocationExists() {
    return await this.getOrCreateDefaultPickupLocation();
  }

  // Track shipment
  async trackShipment(awb) {
    try {
      const headers = await this.getAuthHeaders();
      const response = await axios.get(`${this.baseURL}/courier/track/awb/${awb}`, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to track shipment:', error.response?.data || error.message);
      throw new Error(`Failed to track shipment: ${error.response?.data?.message || error.message}`);
    }
  }
}

module.exports = new ShiprocketService();
