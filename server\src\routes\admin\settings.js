const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const {
  getSettings,
  getSettingsByCategory,
  updateSettings,
  updateSetting,
  deleteSetting,
  initializeDefaultSettings,
  getShiprocketPickupLocations,
  createDefaultPickupLocation,
  testShiprocketConnection
} = require('../../controllers/admin/settingsController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

/**
 * @route   GET /api/admin/settings
 * @desc    Get all system settings
 * @access  Private (Admin)
 */
router.get('/', getSettings);

/**
 * @route   GET /api/admin/settings/category/:category
 * @desc    Get settings by category
 * @access  Private (Admin)
 */
router.get('/category/:category', getSettingsByCategory);

/**
 * @route   POST /api/admin/settings/initialize
 * @desc    Initialize default settings
 * @access  Private (Admin)
 */
router.post('/initialize', initializeDefaultSettings);

/**
 * @route   PUT /api/admin/settings
 * @desc    Update multiple settings
 * @access  Private (Admin)
 */
router.put('/', updateSettings);

/**
 * @route   PUT /api/admin/settings/:key
 * @desc    Update single setting
 * @access  Private (Admin)
 */
router.put('/:key', updateSetting);

/**
 * @route   DELETE /api/admin/settings/:key
 * @desc    Delete setting
 * @access  Private (Admin)
 */
router.delete('/:key', deleteSetting);

/**
 * @route   GET /api/admin/settings/shiprocket/pickup-locations
 * @desc    Get Shiprocket pickup locations
 * @access  Private (Admin)
 */
router.get('/shiprocket/pickup-locations', getShiprocketPickupLocations);

/**
 * @route   POST /api/admin/settings/shiprocket/create-pickup
 * @desc    Create default Shiprocket pickup location
 * @access  Private (Admin)
 */
router.post('/shiprocket/create-pickup', createDefaultPickupLocation);

/**
 * @route   GET /api/admin/settings/shiprocket/test-connection
 * @desc    Test Shiprocket connection
 * @access  Private (Admin)
 */
router.get('/shiprocket/test-connection', testShiprocketConnection);

module.exports = router;