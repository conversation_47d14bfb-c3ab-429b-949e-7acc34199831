# Design Document

## Overview

This design addresses the unauthorized access error occurring during Shiprocket order creation in the multi-vendor ecommerce platform. The current implementation already has sophisticated vendor-specific pickup location logic, but fails when trying to create pickup locations programmatically due to insufficient Shiprocket account permissions.

The solution focuses on:
1. Fixing the pickup location resolution logic to handle permission errors gracefully
2. Using vendor business addresses directly when pickup location creation fails
3. Simple retry mechanism for transient failures

## Architecture

### Current System Analysis

The existing `ShiprocketService` already implements:
- Multi-vendor order processing with `createOrdersForVendors()`
- Vendor-specific pickup location resolution with priority logic
- Automatic pickup location creation attempts

**Key Issue Identified**: The unauthorized access error (403) occurs when attempting to create pickup locations programmatically. The Shiprocket account lacks permissions to create pickup locations via API.

### Simple Fix Strategy

Instead of complex admin tools and validation systems, the solution is straightforward:

1. **When pickup location creation fails with 403 error**: Skip automatic creation and use existing pickup locations
2. **If no pickup locations exist**: Use a pre-configured default pickup location from environment variables
3. **For vendor-specific orders**: Map vendor business address to the closest available pickup location

## Components and Interfaces

### 1. Enhanced Shiprocket Service Methods

**Enhancements to**: `server/src/services/shiprocketService.js`

```javascript
// Enhanced methods:
async resolvePickupLocationName(preferredName, vendor) // Fix 403 error handling
async handlePickupLocationFailure(vendor, error) // Graceful fallback
async getOrCreateDefaultPickupLocation() // Ensure at least one pickup location exists
```

## Data Models

### Vendor Model (No Changes Required)

The existing Vendor model already has all necessary fields:

```javascript
businessAddress: {
  street: String,
  city: String, 
  state: String,
  zipCode: String,
  country: String
},
shiprocket: {
  pickupLocationName: String,
  pickupLocationCode: String
}
```

No additional fields needed - the solution works with existing data structure.

## Error Handling

### 1. Pickup Location Creation Errors (Simple Fix)

```javascript
// Enhanced error handling in shiprocketService.js
async handlePickupLocationFailure(vendor, error) {
  if (error.response?.status === 403) {
    // Unauthorized - skip automatic creation, use existing locations
    console.log(`⚠️ Cannot create pickup location for ${vendor.businessName} - using existing locations`);
    return await this.getFirstAvailablePickupLocation();
  }
  
  // For other errors, log and continue with fallback
  console.error(`Failed to create pickup location for ${vendor.businessName}:`, error.message);
  return await this.getOrCreateDefaultPickupLocation();
}
```

### 2. Simple Fallback Strategy

```javascript
async getOrCreateDefaultPickupLocation() {
  const locations = await this.fetchPickupLocations();
  
  if (locations.length > 0) {
    return locations[0].pickup_location;
  }
  
  // If no locations exist, ensure we have environment variables set
  if (!process.env.SHIPROCKET_DEFAULT_PICKUP_NAME) {
    throw new Error('No pickup locations found and no default configured. Please set SHIPROCKET_DEFAULT_PICKUP_NAME in environment variables.');
  }
  
  return process.env.SHIPROCKET_DEFAULT_PICKUP_NAME;
}
```

## Testing Strategy

### 1. Unit Tests

**Location**: `server/test/services/`

```javascript
// vendorAddressValidator.test.js
describe('VendorAddressValidator', () => {
  test('validates complete vendor address')
  test('identifies missing required fields')
  test('validates Indian postal codes')
  test('handles international addresses')
})

// shiprocketService.test.js (enhanced)
describe('ShiprocketService Enhanced', () => {
  test('retries order creation on failure')
  test('handles 403 unauthorized errors gracefully')
  test('notifies vendors of pickup location issues')
  test('validates vendor pickup locations')
})
```

### 2. Integration Tests

**Location**: `server/test/integration/`

```javascript
// vendorPickupLocation.test.js
describe('Vendor Pickup Location API', () => {
  test('GET /api/vendor/pickup-location/status returns correct status')
  test('POST /api/vendor/pickup-location/setup creates pickup location')
  test('handles Shiprocket API failures gracefully')
  test('validates vendor address before setup')
})
```

### 3. End-to-End Tests

**Location**: `server/test/e2e/`

```javascript
// orderProcessing.test.js
describe('Multi-vendor Order Processing', () => {
  test('processes order with valid vendor pickup locations')
  test('handles order with invalid vendor addresses')
  test('retries failed order creation')
  test('notifies vendors of pickup location issues')
})
```

## Implementation Phases

### Phase 1: Address Validation & Error Handling
- Implement `VendorAddressValidator`
- Enhance error handling in `ShiprocketService`
- Add vendor notification system

### Phase 2: Vendor Management APIs
- Create vendor pickup location management endpoints
- Implement pickup location status checking
- Add address validation API

### Phase 3: Admin Tools
- Create admin pickup location management interface
- Add vendor pickup location monitoring dashboard
- Implement bulk vendor address validation

### Phase 4: Enhanced Order Processing
- Add retry logic to order creation
- Implement graceful failure handling
- Add comprehensive logging and monitoring

## Security Considerations

### 1. API Security
- Validate vendor ownership before allowing pickup location modifications
- Implement rate limiting on pickup location creation attempts
- Sanitize all address inputs to prevent injection attacks

### 2. Shiprocket API Security
- Secure storage of Shiprocket credentials
- Implement token refresh logic with proper error handling
- Log all API interactions for audit purposes

### 3. Data Privacy
- Encrypt sensitive vendor address information
- Implement proper access controls for admin functions
- Ensure GDPR compliance for vendor data handling

## Performance Considerations

### 1. Caching Strategy
- Cache Shiprocket pickup locations to reduce API calls
- Implement vendor address validation caching
- Use Redis for temporary pickup location status

### 2. Async Processing
- Process pickup location creation asynchronously
- Implement background jobs for address validation
- Use queues for vendor notifications

### 3. Database Optimization
- Index vendor pickup location fields
- Optimize queries for pickup location status checks
- Implement pagination for admin pickup location lists

## Monitoring and Logging

### 1. Metrics to Track
- Pickup location creation success/failure rates
- Order processing success rates by vendor
- Average time to resolve pickup location issues
- Vendor address validation error patterns

### 2. Alerting
- Alert on high pickup location creation failure rates
- Monitor Shiprocket API response times
- Alert on vendors with persistent address validation issues

### 3. Logging Strategy
- Log all pickup location operations with vendor context
- Track order processing failures with detailed error information
- Maintain audit trail for admin pickup location modifications