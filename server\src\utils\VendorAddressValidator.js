class VendorAddressValidator {
  constructor() {
    // Indian state codes for validation
    this.validIndianStates = new Set([
      'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh',
      'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka',
      'Kerala', 'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram',
      'Nagaland', 'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu',
      'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal',
      'Andaman and Nicobar Islands', 'Chandigarh', 'Dadra and Nagar Haveli and Daman and Diu',
      'Delhi', 'Jammu and Kashmir', 'Ladakh', 'Lakshadweep', 'Puducherry'
    ]);
  }

  /**
   * Validate vendor address for Shiprocket compatibility
   * @param {Object} vendor - Vendor object with businessAddress and contactInfo
   * @returns {Object} - { isValid: boolean, errors: string[], warnings: string[] }
   */
  validateVendorAddress(vendor) {
    const errors = [];
    const warnings = [];

    if (!vendor) {
      errors.push('Vendor is required');
      return { isValid: false, errors, warnings };
    }

    // Validate business name
    if (!vendor.businessName || vendor.businessName.trim() === '') {
      errors.push('Business name is required');
    } else if (vendor.businessName.trim().length < 2) {
      errors.push('Business name must be at least 2 characters long');
    }

    // Validate business address
    if (!vendor.businessAddress) {
      errors.push('Business address is required');
    } else {
      this.validateAddressFields(vendor.businessAddress, errors, warnings);
    }

    // Validate contact information
    if (!vendor.contactInfo) {
      errors.push('Contact information is required');
    } else {
      this.validateContactInfo(vendor.contactInfo, errors, warnings);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate address fields
   * @param {Object} address - Business address object
   * @param {Array} errors - Array to collect errors
   * @param {Array} warnings - Array to collect warnings
   */
  validateAddressFields(address, errors, warnings) {
    // Required fields
    const requiredFields = [
      { field: 'street', name: 'Street address' },
      { field: 'city', name: 'City' },
      { field: 'state', name: 'State' },
      { field: 'zipCode', name: 'ZIP/PIN code' }
    ];

    requiredFields.forEach(({ field, name }) => {
      if (!address[field] || address[field].trim() === '') {
        errors.push(`${name} is required`);
      }
    });

    // Validate street address
    if (address.street && address.street.trim().length < 5) {
      warnings.push('Street address seems too short, please ensure it\'s complete');
    }

    // Validate city
    if (address.city && address.city.trim().length < 2) {
      errors.push('City name must be at least 2 characters long');
    }

    // Validate state
    if (address.state) {
      const state = address.state.trim();
      if (!this.validIndianStates.has(state)) {
        warnings.push(`State '${state}' may not be recognized. Please use full state name (e.g., 'Maharashtra' instead of 'MH')`);
      }
    }

    // Validate ZIP/PIN code
    if (address.zipCode) {
      const zipCode = address.zipCode.trim();
      if (!this.isValidIndianPincode(zipCode)) {
        errors.push('ZIP/PIN code must be a valid 6-digit Indian PIN code');
      }
    }

    // Validate country (optional, defaults to India)
    if (address.country && address.country.trim() !== '' && address.country.trim() !== 'India') {
      warnings.push('International shipping may have different requirements');
    }
  }

  /**
   * Validate contact information
   * @param {Object} contactInfo - Contact information object
   * @param {Array} errors - Array to collect errors
   * @param {Array} warnings - Array to collect warnings
   */
  validateContactInfo(contactInfo, errors, warnings) {
    // Validate business phone
    if (!contactInfo.businessPhone || contactInfo.businessPhone.trim() === '') {
      errors.push('Business phone number is required');
    } else {
      const phone = contactInfo.businessPhone.trim();
      if (!this.isValidIndianPhoneNumber(phone)) {
        errors.push('Business phone number must be a valid Indian mobile number (10 digits)');
      }
    }

    // Validate business email (optional but recommended)
    if (contactInfo.businessEmail) {
      const email = contactInfo.businessEmail.trim();
      if (!this.isValidEmail(email)) {
        errors.push('Business email must be a valid email address');
      }
    } else {
      warnings.push('Business email is recommended for better communication');
    }
  }

  /**
   * Validate Indian PIN code
   * @param {string} pincode - PIN code to validate
   * @returns {boolean} - True if valid
   */
  isValidIndianPincode(pincode) {
    const pincodeRegex = /^[1-9][0-9]{5}$/;
    return pincodeRegex.test(pincode);
  }

  /**
   * Validate Indian phone number
   * @param {string} phone - Phone number to validate
   * @returns {boolean} - True if valid
   */
  isValidIndianPhoneNumber(phone) {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    
    // Check for 10-digit mobile number or 11-digit with country code
    const mobileRegex = /^[6-9]\d{9}$/; // 10-digit mobile
    const withCountryCodeRegex = /^91[6-9]\d{9}$/; // 11-digit with +91
    
    return mobileRegex.test(cleanPhone) || withCountryCodeRegex.test(cleanPhone);
  }

  /**
   * Validate email address
   * @param {string} email - Email to validate
   * @returns {boolean} - True if valid
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get missing required fields for vendor address
   * @param {Object} vendor - Vendor object
   * @returns {Array} - Array of missing field names
   */
  getMissingRequiredFields(vendor) {
    const missing = [];

    if (!vendor) return ['vendor'];

    if (!vendor.businessName || vendor.businessName.trim() === '') {
      missing.push('businessName');
    }

    if (!vendor.businessAddress) {
      missing.push('businessAddress');
    } else {
      const address = vendor.businessAddress;
      if (!address.street || address.street.trim() === '') missing.push('businessAddress.street');
      if (!address.city || address.city.trim() === '') missing.push('businessAddress.city');
      if (!address.state || address.state.trim() === '') missing.push('businessAddress.state');
      if (!address.zipCode || address.zipCode.trim() === '') missing.push('businessAddress.zipCode');
    }

    if (!vendor.contactInfo) {
      missing.push('contactInfo');
    } else {
      if (!vendor.contactInfo.businessPhone || vendor.contactInfo.businessPhone.trim() === '') {
        missing.push('contactInfo.businessPhone');
      }
    }

    return missing;
  }

  /**
   * Check if vendor address is complete for Shiprocket
   * @param {Object} vendor - Vendor object
   * @returns {boolean} - True if address is complete
   */
  isAddressComplete(vendor) {
    const validation = this.validateVendorAddress(vendor);
    return validation.isValid;
  }

  /**
   * Format validation results for logging
   * @param {Object} validation - Validation result object
   * @param {string} vendorName - Vendor name for context
   * @returns {string} - Formatted message
   */
  formatValidationMessage(validation, vendorName = 'Unknown') {
    if (validation.isValid) {
      return `✅ Vendor address validation passed for ${vendorName}`;
    }

    let message = `❌ Vendor address validation failed for ${vendorName}:\n`;
    
    if (validation.errors.length > 0) {
      message += `  Errors: ${validation.errors.join(', ')}\n`;
    }
    
    if (validation.warnings.length > 0) {
      message += `  Warnings: ${validation.warnings.join(', ')}`;
    }

    return message.trim();
  }
}

module.exports = VendorAddressValidator;