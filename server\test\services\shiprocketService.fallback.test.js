const ShiprocketService = require('../../src/services/shiprocketService');

// Mock axios
jest.mock('axios');
const axios = require('axios');

describe('ShiprocketService Fallback Mechanism', () => {
  let service;
  let originalEnv;

  beforeEach(() => {
    service = new ShiprocketService();
    originalEnv = process.env;
    jest.clearAllMocks();
    
    // Mock successful authentication
    axios.post.mockResolvedValue({
      data: { token: 'mock-token' }
    });
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('getOrCreateDefaultPickupLocation', () => {
    test('should use existing pickup location when available', async () => {
      const mockLocations = [
        { pickup_location: 'Existing Warehouse' },
        { pickup_location: 'Another Location' }
      ];

      axios.get.mockResolvedValue({
        data: { data: mockLocations }
      });

      const result = await service.getOrCreateDefaultPickupLocation();
      
      expect(result).toBe('Existing Warehouse');
    });

    test('should find best default location by name matching', async () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Main Warehouse';
      
      const mockLocations = [
        { pickup_location: 'Secondary Location' },
        { pickup_location: 'Main Warehouse' },
        { pickup_location: 'Third Location' }
      ];

      axios.get.mockResolvedValue({
        data: { data: mockLocations }
      });

      const result = await service.getOrCreateDefaultPickupLocation();
      
      expect(result).toBe('Main Warehouse');
    });

    test('should find common default names when exact match not found', async () => {
      const mockLocations = [
        { pickup_location: 'Company Main Warehouse' },
        { pickup_location: 'Secondary Location' }
      ];

      axios.get.mockResolvedValue({
        data: { data: mockLocations }
      });

      const result = await service.getOrCreateDefaultPickupLocation();
      
      expect(result).toBe('Company Main Warehouse');
    });

    test('should use environment variable when no locations exist', async () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Configured Warehouse';
      
      axios.get.mockResolvedValue({
        data: { data: [] }
      });

      const result = await service.getOrCreateDefaultPickupLocation();
      
      expect(result).toBe('Configured Warehouse');
    });

    test('should use generic fallback when no configuration exists', async () => {
      delete process.env.SHIPROCKET_DEFAULT_PICKUP_NAME;
      
      axios.get.mockResolvedValue({
        data: { data: [] }
      });

      const result = await service.getOrCreateDefaultPickupLocation();
      
      expect(result).toBe('Main Warehouse');
    });

    test('should handle network errors with emergency fallback', async () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Emergency Location';
      
      // First call fails, second call also fails
      axios.get.mockRejectedValueOnce(new Error('Network error'));
      axios.get.mockRejectedValueOnce(new Error('Network still down'));

      const result = await service.getOrCreateDefaultPickupLocation();
      
      expect(result).toBe('Emergency Location');
    });
  });

  describe('hasCompleteDefaultConfig', () => {
    test('should return true when all required config is present', () => {
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      process.env.SHIPROCKET_DEFAULT_CITY = 'Mumbai';
      process.env.SHIPROCKET_DEFAULT_STATE = 'Maharashtra';
      process.env.SHIPROCKET_DEFAULT_PINCODE = '400001';

      expect(service.hasCompleteDefaultConfig()).toBe(true);
    });

    test('should return false when required config is missing', () => {
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      process.env.SHIPROCKET_DEFAULT_CITY = 'Mumbai';
      // Missing state and pincode

      expect(service.hasCompleteDefaultConfig()).toBe(false);
    });
  });

  describe('validateDefaultPickupConfig', () => {
    test('should validate complete configuration', () => {
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      process.env.SHIPROCKET_DEFAULT_CITY = 'Mumbai';
      process.env.SHIPROCKET_DEFAULT_STATE = 'Maharashtra';
      process.env.SHIPROCKET_DEFAULT_PINCODE = '400001';
      process.env.SHIPROCKET_DEFAULT_PHONE = '9876543210';

      const result = service.validateDefaultPickupConfig();
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should identify missing required fields', () => {
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      // Missing other required fields

      const result = service.validateDefaultPickupConfig();
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Default city (SHIPROCKET_DEFAULT_CITY) is required');
      expect(result.errors).toContain('Default state (SHIPROCKET_DEFAULT_STATE) is required');
      expect(result.errors).toContain('Default PIN code (SHIPROCKET_DEFAULT_PINCODE) is required');
    });

    test('should validate PIN code format', () => {
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      process.env.SHIPROCKET_DEFAULT_CITY = 'Mumbai';
      process.env.SHIPROCKET_DEFAULT_STATE = 'Maharashtra';
      process.env.SHIPROCKET_DEFAULT_PINCODE = '12345'; // Invalid - only 5 digits

      const result = service.validateDefaultPickupConfig();
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Default PIN code must be a valid 6-digit Indian PIN code');
    });

    test('should validate phone number format', () => {
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      process.env.SHIPROCKET_DEFAULT_CITY = 'Mumbai';
      process.env.SHIPROCKET_DEFAULT_STATE = 'Maharashtra';
      process.env.SHIPROCKET_DEFAULT_PINCODE = '400001';
      process.env.SHIPROCKET_DEFAULT_PHONE = '1234567890'; // Invalid - starts with 1

      const result = service.validateDefaultPickupConfig();
      
      expect(result.isValid).toBe(true); // Still valid, but should have warning
      expect(result.warnings).toContain('Default phone number should be a valid Indian mobile number');
    });
  });

  describe('createDefaultPickupLocation', () => {
    test('should create pickup location with valid configuration', async () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Test Warehouse';
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      process.env.SHIPROCKET_DEFAULT_CITY = 'Mumbai';
      process.env.SHIPROCKET_DEFAULT_STATE = 'Maharashtra';
      process.env.SHIPROCKET_DEFAULT_PINCODE = '400001';
      process.env.SHIPROCKET_DEFAULT_PHONE = '9876543210';

      axios.post.mockResolvedValueOnce({ data: { token: 'mock-token' } }); // auth
      axios.post.mockResolvedValueOnce({ data: { pickup_id: 'PL123' } }); // creation

      const result = await service.createDefaultPickupLocation();
      
      expect(result.pickup_id).toBe('PL123');
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/settings/company/addpickup'),
        expect.objectContaining({
          pickup_location: 'Test Warehouse',
          address: '123 Test Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          pin_code: '400001',
          phone: '9876543210'
        }),
        expect.any(Object)
      );
    });

    test('should handle 403 unauthorized error gracefully', async () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Test Warehouse';
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      process.env.SHIPROCKET_DEFAULT_CITY = 'Mumbai';
      process.env.SHIPROCKET_DEFAULT_STATE = 'Maharashtra';
      process.env.SHIPROCKET_DEFAULT_PINCODE = '400001';

      axios.post.mockResolvedValueOnce({ data: { token: 'mock-token' } }); // auth
      axios.post.mockRejectedValueOnce({
        response: { 
          status: 403, 
          data: { message: 'Unauthorized' } 
        }
      }); // creation fails

      await expect(service.createDefaultPickupLocation()).rejects.toThrow('Failed to create default pickup location: Unauthorized');
    });

    test('should reject creation with incomplete configuration', async () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Test Warehouse';
      // Missing required address fields

      await expect(service.createDefaultPickupLocation()).rejects.toThrow('Cannot create default pickup location:');
    });
  });

  describe('getDefaultPickupLocationStatus', () => {
    test('should return status with basic configuration', () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Test Warehouse';

      const status = service.getDefaultPickupLocationStatus();
      
      expect(status.hasBasicConfig).toBe(true);
      expect(status.hasCompleteConfig).toBe(false);
      expect(status.recommendedAction).toContain('manually create');
    });

    test('should return status with complete configuration', () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Test Warehouse';
      process.env.SHIPROCKET_DEFAULT_ADDRESS = '123 Test Street';
      process.env.SHIPROCKET_DEFAULT_CITY = 'Mumbai';
      process.env.SHIPROCKET_DEFAULT_STATE = 'Maharashtra';
      process.env.SHIPROCKET_DEFAULT_PINCODE = '400001';

      const status = service.getDefaultPickupLocationStatus();
      
      expect(status.hasBasicConfig).toBe(true);
      expect(status.hasCompleteConfig).toBe(true);
      expect(status.recommendedAction).toContain('auto-create');
    });
  });

  describe('testDefaultPickupLocationConfig', () => {
    test('should test configuration and provide feedback', async () => {
      process.env.SHIPROCKET_DEFAULT_PICKUP_NAME = 'Test Warehouse';
      
      const mockLocations = [
        { pickup_location: 'Test Warehouse' },
        { pickup_location: 'Another Location' }
      ];

      axios.get.mockResolvedValue({
        data: { data: mockLocations }
      });

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const status = await service.testDefaultPickupLocationConfig();
      
      expect(status.hasBasicConfig).toBe(true);
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Found 2 existing pickup location(s)'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Default name match: ✅'));

      consoleSpy.mockRestore();
    });
  });
});