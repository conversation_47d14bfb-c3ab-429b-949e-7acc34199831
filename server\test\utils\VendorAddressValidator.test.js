const VendorAddressValidator = require('../../src/utils/VendorAddressValidator');

describe('VendorAddressValidator', () => {
  let validator;

  beforeEach(() => {
    validator = new VendorAddressValidator();
  });

  describe('validateVendorAddress', () => {
    test('should validate complete vendor address', () => {
      const vendor = {
        businessName: 'Test Business',
        businessAddress: {
          street: '123 Test Street, Test Area',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001',
          country: 'India'
        },
        contactInfo: {
          businessPhone: '**********',
          businessEmail: '<EMAIL>'
        }
      };

      const result = validator.validateVendorAddress(vendor);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should identify missing required fields', () => {
      const vendor = {
        businessName: '',
        businessAddress: {
          street: '',
          city: 'Mumbai',
          state: '',
          zipCode: '400001'
        },
        contactInfo: {
          businessPhone: '',
          businessEmail: 'invalid-email'
        }
      };

      const result = validator.validateVendorAddress(vendor);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Business name is required');
      expect(result.errors).toContain('Street address is required');
      expect(result.errors).toContain('State is required');
      expect(result.errors).toContain('Business phone number is required');
      expect(result.errors).toContain('Business email must be a valid email address');
    });

    test('should validate Indian postal codes', () => {
      const validPincodes = ['400001', '110001', '560001'];
      const invalidPincodes = ['0000001', '12345', 'ABCDEF', '000001'];

      validPincodes.forEach(pincode => {
        expect(validator.isValidIndianPincode(pincode)).toBe(true);
      });

      invalidPincodes.forEach(pincode => {
        expect(validator.isValidIndianPincode(pincode)).toBe(false);
      });
    });

    test('should validate Indian phone numbers', () => {
      const validPhones = ['**********', '**********', '************', '+************'];
      const invalidPhones = ['**********', '12345', 'abcdefghij', '**********'];

      validPhones.forEach(phone => {
        expect(validator.isValidIndianPhoneNumber(phone)).toBe(true);
      });

      invalidPhones.forEach(phone => {
        expect(validator.isValidIndianPhoneNumber(phone)).toBe(false);
      });
    });

    test('should handle international addresses with warnings', () => {
      const vendor = {
        businessName: 'International Business',
        businessAddress: {
          street: '123 International Street',
          city: 'New York',
          state: 'New York',
          zipCode: '400001', // Using Indian PIN for test
          country: 'USA'
        },
        contactInfo: {
          businessPhone: '**********',
          businessEmail: '<EMAIL>'
        }
      };

      const result = validator.validateVendorAddress(vendor);
      
      expect(result.warnings).toContain('International shipping may have different requirements');
      expect(result.warnings).toContain('State \'New York\' may not be recognized. Please use full state name (e.g., \'Maharashtra\' instead of \'MH\')');
    });
  });

  describe('getMissingRequiredFields', () => {
    test('should return missing fields for incomplete vendor', () => {
      const vendor = {
        businessName: 'Test Business',
        businessAddress: {
          street: '123 Test Street',
          city: 'Mumbai'
          // missing state and zipCode
        },
        contactInfo: {
          // missing businessPhone
          businessEmail: '<EMAIL>'
        }
      };

      const missing = validator.getMissingRequiredFields(vendor);
      
      expect(missing).toContain('businessAddress.state');
      expect(missing).toContain('businessAddress.zipCode');
      expect(missing).toContain('contactInfo.businessPhone');
    });

    test('should return empty array for complete vendor', () => {
      const vendor = {
        businessName: 'Test Business',
        businessAddress: {
          street: '123 Test Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001'
        },
        contactInfo: {
          businessPhone: '**********'
        }
      };

      const missing = validator.getMissingRequiredFields(vendor);
      
      expect(missing).toHaveLength(0);
    });
  });

  describe('isAddressComplete', () => {
    test('should return true for complete address', () => {
      const vendor = {
        businessName: 'Test Business',
        businessAddress: {
          street: '123 Test Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001'
        },
        contactInfo: {
          businessPhone: '**********'
        }
      };

      expect(validator.isAddressComplete(vendor)).toBe(true);
    });

    test('should return false for incomplete address', () => {
      const vendor = {
        businessName: 'Test Business',
        businessAddress: {
          street: '123 Test Street',
          city: 'Mumbai'
          // missing state and zipCode
        },
        contactInfo: {
          businessPhone: '**********'
        }
      };

      expect(validator.isAddressComplete(vendor)).toBe(false);
    });
  });

  describe('formatValidationMessage', () => {
    test('should format success message', () => {
      const validation = { isValid: true, errors: [], warnings: [] };
      const message = validator.formatValidationMessage(validation, 'Test Vendor');
      
      expect(message).toBe('✅ Vendor address validation passed for Test Vendor');
    });

    test('should format error message with errors and warnings', () => {
      const validation = {
        isValid: false,
        errors: ['Business name is required', 'Phone is required'],
        warnings: ['Email is recommended']
      };
      const message = validator.formatValidationMessage(validation, 'Test Vendor');
      
      expect(message).toContain('❌ Vendor address validation failed for Test Vendor');
      expect(message).toContain('Errors: Business name is required, Phone is required');
      expect(message).toContain('Warnings: Email is recommended');
    });
  });
});