#!/usr/bin/env node

/**
 * Shiprocket Configuration Test Script
 * 
 * This script helps you test your Shiprocket configuration and pickup location setup.
 * Run this script to verify your environment variables and test connectivity.
 * 
 * Usage: node scripts/test-shiprocket-config.js
 */

require('dotenv').config();
const ShiprocketService = require('../src/services/shiprocketService');

async function testShiprocketConfiguration() {
  console.log('🚀 Shiprocket Configuration Test\n');
  
  try {
    const service = new ShiprocketService();
    
    // Test 1: Check environment variables
    console.log('📋 Step 1: Checking Environment Variables');
    console.log('=' .repeat(50));
    
    const requiredVars = ['SHIPROCKET_EMAIL', 'SHIPROCKET_PASSWORD'];
    const optionalVars = [
      'SHIPROCKET_DEFAULT_PICKUP_NAME',
      'SHIPROCKET_DEFAULT_ADDRESS',
      'SHIPROCKET_DEFAULT_CITY',
      'SHIPROCKET_DEFAULT_STATE',
      'SHIPROCKET_DEFAULT_PINCODE',
      'SHIPROCKET_DEFAULT_PHONE'
    ];
    
    let hasRequiredVars = true;
    
    requiredVars.forEach(varName => {
      const value = process.env[varName];
      if (value) {
        console.log(`✅ ${varName}: ${varName === 'SHIPROCKET_PASSWORD' ? '***' : value}`);
      } else {
        console.log(`❌ ${varName}: Not set`);
        hasRequiredVars = false;
      }
    });
    
    console.log('\nOptional Configuration:');
    optionalVars.forEach(varName => {
      const value = process.env[varName];
      console.log(`${value ? '✅' : '⚠️ '} ${varName}: ${value || 'Not set'}`);
    });
    
    if (!hasRequiredVars) {
      console.log('\n❌ Missing required environment variables. Please set them in your .env file.');
      return;
    }
    
    // Test 2: Authentication
    console.log('\n📋 Step 2: Testing Shiprocket Authentication');
    console.log('=' .repeat(50));
    
    try {
      await service.authenticate();
      console.log('✅ Authentication successful');
    } catch (authError) {
      console.log(`❌ Authentication failed: ${authError.message}`);
      console.log('Please check your SHIPROCKET_EMAIL and SHIPROCKET_PASSWORD');
      return;
    }
    
    // Test 3: Pickup Location Configuration
    console.log('\n📋 Step 3: Testing Default Pickup Location Configuration');
    console.log('=' .repeat(50));
    
    await service.testDefaultPickupLocationConfig();
    
    // Test 4: Test Fallback Mechanism
    console.log('\n📋 Step 4: Testing Fallback Mechanism');
    console.log('=' .repeat(50));
    
    try {
      const fallbackLocation = await service.getOrCreateDefaultPickupLocation();
      console.log(`✅ Fallback mechanism working. Default location: ${fallbackLocation}`);
    } catch (fallbackError) {
      console.log(`❌ Fallback mechanism failed: ${fallbackError.message}`);
    }
    
    // Test 5: Summary and Recommendations
    console.log('\n📋 Step 5: Summary and Recommendations');
    console.log('=' .repeat(50));
    
    const status = service.getDefaultPickupLocationStatus();
    
    if (status.hasBasicConfig && status.hasCompleteConfig) {
      console.log('🎉 Configuration is complete! Your system should handle pickup location issues gracefully.');
    } else if (status.hasBasicConfig) {
      console.log('⚠️  Basic configuration is present, but consider setting complete address details for auto-creation capability.');
    } else {
      console.log('❌ Configuration is incomplete. Please set at least SHIPROCKET_DEFAULT_PICKUP_NAME.');
    }
    
    console.log(`\n💡 Next Steps: ${status.recommendedAction}`);
    
    console.log('\n🔗 Useful Links:');
    console.log('   - Shiprocket Dashboard: https://app.shiprocket.in');
    console.log('   - Pickup Locations: https://app.shiprocket.in/settings/company-profile');
    console.log('   - API Documentation: https://apidocs.shiprocket.in/');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.log('\nPlease check your configuration and try again.');
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testShiprocketConfiguration()
    .then(() => {
      console.log('\n✅ Configuration test completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Configuration test failed:', error.message);
      process.exit(1);
    });
}

module.exports = testShiprocketConfiguration;